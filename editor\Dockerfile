FROM node:22-alpine AS builder

WORKDIR /app

# 复制engine依赖（已预构建）
COPY engine ./engine

# 复制编辑器的package.json和package-lock.json
COPY editor/package*.json ./

# 临时修改package.json中的engine路径
RUN sed -i 's|"dl-engine-core": "file:../engine"|"dl-engine-core": "file:./engine"|g' package.json

# 设置npm配置
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm config set fund false && \
    npm config set audit false

# 安装依赖
RUN npm install --verbose

# 复制编辑器源代码（排除node_modules）
COPY editor/src ./src
COPY editor/public ./public
COPY editor/scripts ./scripts
COPY editor/index.html ./
COPY editor/vite.config.ts ./
COPY editor/tsconfig.json ./
COPY editor/tsconfig.node.json ./
COPY editor/jest.config.js ./

# 设置环境变量
ENV REACT_APP_API_URL=/api
ENV REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007
ENV REACT_APP_MINIO_ENDPOINT=http://localhost:9000
ENV REACT_APP_ENVIRONMENT=production

# 构建应用
RUN npm run build

# 注入环境变量到构建产物
RUN node scripts/inject-env.js

# 生产环境
FROM nginx:alpine

# 复制构建产物到Nginx服务目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY editor/nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
