/**
 * 冲突面板样式
 */

.conflict-panel {
  position: absolute;
  top: 50px;
  right: 20px;
  width: 600px;
  z-index: 1000;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  
  .conflict-card {
    border-radius: 4px;
    overflow: hidden;
    
    .ant-card-head {
      background-color: #fff7e6;
      border-bottom: 1px solid #ffd591;
      
      .ant-card-head-title {
        padding: 12px 0;
        
        span {
          white-space: nowrap;
          overflow: visible;
        }
      }
      
      .ant-card-extra {
        .ant-btn {
          border: none;
          box-shadow: none;
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.04);
          }
        }
      }
    }
    
    .ant-card-body {
      padding: 16px;
      max-height: 500px;
      overflow-y: auto;
    }
  }
  
  .conflict-list {
    .conflict-item {
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 4px;
      margin-bottom: 8px;
      padding: 8px;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.selected {
        background-color: #e6f7ff;
        border: 1px solid #91d5ff;
      }
      
      .conflict-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(250, 173, 20, 0.1);
      }
      
      .ant-list-item-meta-title {
        margin-bottom: 4px;
        
        .ant-tag {
          margin-left: 8px;
        }
      }
      
      .ant-list-item-meta-description {
        color: rgba(0, 0, 0, 0.65);
        
        div {
          margin-bottom: 4px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .conflict-details {
    margin-top: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    
    .ant-typography {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .conflict-actions {
    margin-top: 16px;
    text-align: center;
    
    .ant-btn {
      margin: 0 4px;
    }
  }
  
  .ant-tabs {
    .ant-tabs-tab {
      .anticon {
        margin-right: 4px;
      }
      
      .ant-badge {
        margin-left: 4px;
      }
    }
    
    .ant-tabs-content {
      padding-top: 16px;
    }
  }
  
  .ant-empty {
    padding: 40px 0;
  }
}

/* 确保面板在移动设备上的响应式显示 */
@media (max-width: 768px) {
  .conflict-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    z-index: 1001;
    
    .conflict-card {
      height: 100%;
      border-radius: 0;
      
      .ant-card-body {
        max-height: calc(100vh - 120px);
      }
    }
  }
}

/* 冲突状态标签样式 */
.conflict-status-pending {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #d48806;
}

.conflict-status-resolved {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #389e0d;
}

.conflict-status-rejected {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #cf1322;
}

/* 冲突类型图标样式 */
.conflict-type-entity {
  color: #1890ff;
}

.conflict-type-component {
  color: #722ed1;
}

.conflict-type-property {
  color: #fa8c16;
}

.conflict-type-transform {
  color: #52c41a;
}

.conflict-type-material {
  color: #eb2f96;
}

.conflict-type-animation {
  color: #13c2c2;
}
