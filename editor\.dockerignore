# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/
.pnpm-debug.log*

# 构建输出
dist/
build/
coverage/
.nyc_output/

# 环境文件
.env
.env.local
.env.development
.env.test
.env.production

# 日志文件
logs/
*.log

# 临时文件
.tmp/
.temp/
.cache/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*.sublime-*

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# 测试相关
__tests__/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx
jest.config.js
setupTests.ts

# 开发工具
.eslintrc*
.prettierrc*
# tsconfig*.json  # 构建时需要
# vite.config*.ts # 构建时需要

# 文档
README.md
*.md
docs/

# 脚本文件
# scripts/  # 构建时需要inject-env.js
*.bat
*.sh

# 其他开发文件
.history/
.vite/
.turbo/
.next/
.nuxt/
.output/

# 编辑器特定的开发文件
fix-*.js
check-*.js
batch-*.js
verify-*.js
test-*.js
test-*.cjs
run-*.bat
*.md

# 包文件
*.tgz
*.tar.gz

# 锁文件（保留 package-lock.json）
yarn.lock
pnpm-lock.yaml

# 保留必要的文件
!package.json
!package-lock.json
!nginx.conf
!index.html
!src/
!public/
!scripts/
!tsconfig.json
!tsconfig.node.json
!vite.config.ts
!jest.config.js

# 类型声明文件（开发时）
*.d.ts.map

# 源码映射文件
*.map

# 编辑器配置文件
.editorconfig

# Linting
.eslintcache

# 性能分析
.clinic/
.0x/

# 其他
.aider*
