import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'DLEngine',
      fileName: (format) => format === 'es' ? 'dl-engine.js' : `dl-engine.${format}.js`,
      formats: ['es', 'umd']
    },
    rollupOptions: {
      external: ['three', 'cannon-es', 'uuid', 'i18next', '@msgpack/msgpack', 'cbor-web', 'lz-string'],
      output: {
        globals: {
          three: 'THREE',
          'cannon-es': 'CANNON',
          uuid: 'uuid',
          i18next: 'i18next',
          '@msgpack/msgpack': 'msgpack',
          'cbor-web': 'CBOR',
          'lz-string': 'LZString',
        },
      },
    },
    emptyOutDir: true,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
});
